# AI工具门户数据库设计

## 数据库表结构设计

基于爬取的数据结构，设计以下数据库表：

### 1. 工具基本信息表 (ai_tools)

```sql
CREATE TABLE ai_tools (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    website_url VARCHAR(500) NOT NULL UNIQUE,
    description TEXT,
    short_description VARCHAR(500), -- 简短描述，用于卡片展示
    
    -- 分类和标签
    category_id INTEGER REFERENCES categories(id),
    primary_category VARCHAR(100), -- 主要分类（从爬取数据获取）
    tags TEXT[], -- PostgreSQL数组类型存储标签
    
    -- 定价信息
    pricing_type VARCHAR(50), -- free, freemium, paid, subscription
    pricing_info TEXT, -- 详细定价信息
    
    -- 统计信息
    rating DECIMAL(3,2) DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    favorite_count INTEGER DEFAULT 0,
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, pending
    is_featured BOOLEAN DEFAULT FALSE,
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    last_crawled_at TIMESTAMP,
    
    -- 索引
    CONSTRAINT valid_rating CHECK (rating >= 0 AND rating <= 5)
);

-- 创建索引
CREATE INDEX idx_ai_tools_category ON ai_tools(category_id);
CREATE INDEX idx_ai_tools_status ON ai_tools(status);
CREATE INDEX idx_ai_tools_featured ON ai_tools(is_featured);
CREATE INDEX idx_ai_tools_rating ON ai_tools(rating DESC);
CREATE INDEX idx_ai_tools_view_count ON ai_tools(view_count DESC);
CREATE INDEX idx_ai_tools_tags ON ai_tools USING GIN(tags);
```

### 2. 分类表 (categories)

```sql
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    icon VARCHAR(100), -- 图标名称或URL
    color VARCHAR(7), -- 十六进制颜色代码
    parent_id INTEGER REFERENCES categories(id), -- 支持层级分类
    sort_order INTEGER DEFAULT 0,
    tool_count INTEGER DEFAULT 0, -- 该分类下的工具数量
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_categories_parent ON categories(parent_id);
CREATE INDEX idx_categories_sort ON categories(sort_order);
```

### 3. 工具媒体资源表 (tool_media)

```sql
CREATE TABLE tool_media (
    id SERIAL PRIMARY KEY,
    tool_id INTEGER NOT NULL REFERENCES ai_tools(id) ON DELETE CASCADE,
    media_type VARCHAR(20) NOT NULL, -- logo, favicon, hero_image, screenshot, video
    url VARCHAR(500) NOT NULL,
    local_path VARCHAR(500), -- 本地存储路径
    alt_text VARCHAR(255),
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE, -- 是否为主要媒体
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_tool_media_tool ON tool_media(tool_id);
CREATE INDEX idx_tool_media_type ON tool_media(media_type);
```

### 4. 工具特性表 (tool_features)

```sql
CREATE TABLE tool_features (
    id SERIAL PRIMARY KEY,
    tool_id INTEGER NOT NULL REFERENCES ai_tools(id) ON DELETE CASCADE,
    feature_text TEXT NOT NULL,
    feature_type VARCHAR(50), -- core, advanced, premium
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_tool_features_tool ON tool_features(tool_id);
```

### 5. 工具落地页信息表 (tool_landpage_info)

```sql
CREATE TABLE tool_landpage_info (
    id SERIAL PRIMARY KEY,
    tool_id INTEGER NOT NULL REFERENCES ai_tools(id) ON DELETE CASCADE,
    page_title VARCHAR(500),
    meta_description TEXT,
    full_page_text TEXT, -- 完整页面文本，用于搜索
    headings JSONB, -- 存储页面标题结构
    links JSONB, -- 存储页面链接信息
    
    -- SEO相关
    keywords TEXT[],
    language VARCHAR(10) DEFAULT 'en',
    
    -- 页面分析
    page_load_time INTEGER, -- 毫秒
    page_size INTEGER, -- 字节
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_tool_landpage_tool ON tool_landpage_info(tool_id);
CREATE INDEX idx_tool_landpage_text ON tool_landpage_info USING GIN(to_tsvector('english', full_page_text));
```

### 6. 工具评价表 (tool_reviews)

```sql
CREATE TABLE tool_reviews (
    id SERIAL PRIMARY KEY,
    tool_id INTEGER NOT NULL REFERENCES ai_tools(id) ON DELETE CASCADE,
    user_id INTEGER, -- 如果有用户系统
    reviewer_name VARCHAR(100),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    pros TEXT[],
    cons TEXT[],
    is_verified BOOLEAN DEFAULT FALSE,
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_tool_reviews_tool ON tool_reviews(tool_id);
CREATE INDEX idx_tool_reviews_rating ON tool_reviews(rating);
```

### 7. 工具使用统计表 (tool_analytics)

```sql
CREATE TABLE tool_analytics (
    id SERIAL PRIMARY KEY,
    tool_id INTEGER NOT NULL REFERENCES ai_tools(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    views INTEGER DEFAULT 0,
    clicks INTEGER DEFAULT 0,
    favorites INTEGER DEFAULT 0,
    shares INTEGER DEFAULT 0,
    
    UNIQUE(tool_id, date)
);

CREATE INDEX idx_tool_analytics_tool_date ON tool_analytics(tool_id, date);
CREATE INDEX idx_tool_analytics_date ON tool_analytics(date);
```

### 8. 工具标签表 (tags)

```sql
CREATE TABLE tags (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    color VARCHAR(7),
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 工具标签关联表
CREATE TABLE tool_tags (
    tool_id INTEGER NOT NULL REFERENCES ai_tools(id) ON DELETE CASCADE,
    tag_id INTEGER NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    PRIMARY KEY (tool_id, tag_id)
);

CREATE INDEX idx_tool_tags_tool ON tool_tags(tool_id);
CREATE INDEX idx_tool_tags_tag ON tool_tags(tag_id);
```

## 视图定义

### 工具完整信息视图

```sql
CREATE VIEW tool_complete_view AS
SELECT 
    t.*,
    c.name as category_name,
    c.slug as category_slug,
    c.icon as category_icon,
    c.color as category_color,
    
    -- 聚合媒体信息
    (SELECT url FROM tool_media WHERE tool_id = t.id AND media_type = 'logo' AND is_primary = true LIMIT 1) as logo_url,
    (SELECT url FROM tool_media WHERE tool_id = t.id AND media_type = 'favicon' LIMIT 1) as favicon_url,
    (SELECT url FROM tool_media WHERE tool_id = t.id AND media_type = 'screenshot' AND is_primary = true LIMIT 1) as screenshot_url,
    
    -- 聚合特性
    (SELECT array_agg(feature_text ORDER BY sort_order) FROM tool_features WHERE tool_id = t.id) as features,
    
    -- 聚合标签
    (SELECT array_agg(tags.name) FROM tool_tags JOIN tags ON tool_tags.tag_id = tags.id WHERE tool_tags.tool_id = t.id) as tag_names,
    
    -- 落地页信息
    tli.page_title,
    tli.meta_description,
    tli.language
    
FROM ai_tools t
LEFT JOIN categories c ON t.category_id = c.id
LEFT JOIN tool_landpage_info tli ON t.id = tli.tool_id
WHERE t.status = 'active';
```

## 数据迁移脚本

基于爬取的数据格式，需要创建数据迁移脚本将JSON数据导入到数据库中。

### 主要字段映射

- `name` → `ai_tools.name`
- `website_url` → `ai_tools.website_url`
- `product_info.description` → `ai_tools.description`
- `product_info.category` → `ai_tools.primary_category`
- `product_info.tags` → `tags` 表和 `tool_tags` 关联表
- `product_info.features` → `tool_features` 表
- `product_info.pricing` → `ai_tools.pricing_info`
- `landpage.logo` → `tool_media` 表 (media_type='logo')
- `landpage.favicon` → `tool_media` 表 (media_type='favicon')
- `landpage.page_screenshot` → `tool_media` 表 (media_type='screenshot')
- `landpage.page_title` → `tool_landpage_info.page_title`
- `landpage.full_page_text` → `tool_landpage_info.full_page_text`
- `landpage.headings` → `tool_landpage_info.headings` (JSONB)
- `landpage.links` → `tool_landpage_info.links` (JSONB)
